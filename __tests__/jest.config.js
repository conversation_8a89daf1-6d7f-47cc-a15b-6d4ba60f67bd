const path = require('path');

module.exports = {
  verbose: true,
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: [path.resolve(__dirname, 'jest.setup.js')],
  rootDir: path.resolve(__dirname, '.'),
  roots: ['<rootDir>', '../newController'],
  moduleFileExtensions: ['js', 'ts', 'json'],
  moduleNameMapper: {
    '^@model/(.*)$': '<rootDir>/../newController/model/$1',
    '^@newController/(.*)$': '<rootDir>/../newController/controller/$1',
    '^@controller/(.*)$': '<rootDir>/../controller/$1',
    '^@commonUtils/(.*)$': '<rootDir>/../commonUtils/$1',
    '^@commonUtils$': '<rootDir>/../commonUtils',
    '^@views/(.*)$': '<rootDir>/../views/$1',
    '^@vendor/(.*)$': '<rootDir>/../vendor/$1'
  },
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: path.resolve(__dirname, 'tsconfig.json')
        // isolatedModules moved to tsconfig.json
      }
    ]
  },
  "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(ts|tsx)$",
  collectCoverage: false,
  collectCoverageFrom: [
    '<rootDir>/../newController/model/form/factory/AddressFactory.ts',
    '<rootDir>/../newController/model/form/factory/DropdownListFactory.ts',
    '<rootDir>/../newController/model/form/factory/EmailAddressFactory.ts',
    '<rootDir>/../newController/model/form/factory/FileUploadFactory.ts',
    '<rootDir>/../newController/model/form/factory/HeadingFactory.ts',
    '<rootDir>/../newController/model/form/factory/ImageFactory.ts',
    '<rootDir>/../newController/model/form/factory/MultiLineTextFactory.ts',
    '<rootDir>/../newController/model/form/factory/MultiSelectionFactory.ts',
    '<rootDir>/../newController/model/form/factory/NumberFactory.ts',
    '<rootDir>/../newController/model/form/factory/ParagraphFactory.ts',
    '<rootDir>/../newController/model/form/factory/PhoneNumberFactory.ts'
  ],
  // coverageProvider: 'v8', // Removed - this option is supported in Jest 29.x but may cause warnings in some setups
  coverageReporters: ['json', 'html', 'text'],
  watchPathIgnorePatterns: ['coverage', 'node_modules'],
  // testTimeout: 30000, // Removed - use setupFilesAfterEnv or individual test timeouts instead
};
