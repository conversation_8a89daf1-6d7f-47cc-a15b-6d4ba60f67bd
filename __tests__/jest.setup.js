// Jest setup file for browser environment
// This file is executed before each test file

// Ensure window object is available
if (typeof window !== 'undefined') {
  // Mock any browser-specific APIs that might be missing
  if (!window.location) {
    window.location = {
      href: 'http://localhost',
      origin: 'http://localhost',
      protocol: 'http:',
      host: 'localhost',
      hostname: 'localhost',
      port: '',
      pathname: '/',
      search: '',
      hash: ''
    };
  }

  // Mock parent window for iframe detection
  if (!window.parent) {
    window.parent = window;
  }

  if (!window.top) {
    window.top = window;
  }
}

// Mock console methods if needed
global.console = {
  ...console,
  // Suppress specific console methods during tests if needed
  // warn: jest.fn(),
  // error: jest.fn(),
};
