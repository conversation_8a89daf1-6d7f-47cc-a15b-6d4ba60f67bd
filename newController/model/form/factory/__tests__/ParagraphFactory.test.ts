// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { ParagraphFactory } from '../ParagraphFactory'
import {
  FormElementParagraphModel,
  ParagraphDefaultProps,
  ParagraphSpecialKeys
} from '@model/form/defines/FormParagraph'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormElementType } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => ({ ...obj })))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
  })
}))

// Mock the getAttrsFromViewModel and getAttrsFromFormField functions
jest.mock('@model/form/common/utils', () => ({
  getAttrsFromViewModel: jest.fn((model, defaultModel, specialKeys) => {
    // Simple mock implementation that returns a FormField
    return {
      id: model.id,
      type: model.type,
      label: model.label,
      text: model.text,
      fieldSpecific: {
        hideLabel: model.hideLabel
      }
    }
  }),
  getAttrsFromFormField: jest.fn((data, defaultModel, specialKeys) => {
    // Simple mock implementation that returns a FormElementParagraphModel
    return {
      ...cloneDeep(defaultModel),
      id: data.id,
      type: data.type,
      label: data.label,
      text: data.text,
      hideLabel: data.fieldSpecific.hideLabel
    }
  })
}))

describe('ParagraphFactory', () => {
  let paragraphFactory: ParagraphFactory
  let mockParagraphModel: FormElementParagraphModel
  let mockFormField: FormField

  beforeEach(() => {
    paragraphFactory = new ParagraphFactory()

    // Create a mock paragraph model for testing
    mockParagraphModel = {
      ...ParagraphDefaultProps,
      id: 'test-id',
      type: FormElementType.Paragraph,
      label: 'Test Paragraph',
      text: 'This is a test paragraph with some content.',
      hideLabel: false
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'Paragraph',
      label: 'Test Paragraph',
      text: 'This is a test paragraph with some content.',
      fieldSpecific: {
        hideLabel: false
      }
    }

    // Reset all mocks before each test
    jest.clearAllMocks()
  })

  describe('create', () => {
    it('should create a new paragraph model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = paragraphFactory.create(option)

      expect(result).toEqual({
        ...ParagraphDefaultProps,
        type: FormElementType.Paragraph,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(ParagraphDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = paragraphFactory.create(option)

      expect(require('@model/form/factory/shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = paragraphFactory.toServerData(mockParagraphModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockParagraphModel.id)
      expect(result.type).toEqual(mockParagraphModel.type)
      expect(result.label).toEqual(mockParagraphModel.label)
      expect(result.text).toEqual(mockParagraphModel.text)
      expect(result.fieldSpecific.hideLabel).toEqual(mockParagraphModel.hideLabel)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {}
      const result = paragraphFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.type).toEqual(mockFormField.type)
      expect(result.label).toEqual(mockFormField.label)
      expect(result.text).toEqual(mockFormField.text)
      expect(result.hideLabel).toEqual(mockFormField.fieldSpecific.hideLabel)
    })
  })

  describe('validate', () => {
    it('should always validate successfully', async () => {
      const validationPromise = paragraphFactory.validate(mockParagraphModel)

      await expect(validationPromise).resolves.toBe(true)
    })
  })

  describe('resetValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockParagraphModel }
      
      paragraphFactory.resetValue(mockParagraphModel)

      // Verify that the model hasn't changed
      expect(mockParagraphModel).toEqual(modelCopy)
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = paragraphFactory.uiOption()

      expect(result).toEqual({
        view: 'FormParagraphView',
        editor: 'FormParagraphOption'
      })
    })
  })

  describe('setValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockParagraphModel }
      const option: FormChangeValueOption = {}
      
      paragraphFactory.setValue(mockParagraphModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockParagraphModel).toEqual(modelCopy)
    })
  })

  describe('syncValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockParagraphModel }
      const option: FormChangeValueOption = {}
      
      paragraphFactory.syncValue(mockParagraphModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockParagraphModel).toEqual(modelCopy)
    })
  })
})
