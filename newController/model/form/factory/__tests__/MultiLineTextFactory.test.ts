// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { MultiLineTextFactory } from '../MultiLineTextFactory'
import {
  MultiLineTextDefaultProps,
  MultiLineTextSpecialKeys,
  FormElementMultiLineTextModel
} from '@model/form/defines/FormMultiLineText'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => ({ ...obj })))

// Mock the processLangResource and removeDDRSource functions
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  }),
  removeDDRSource: jest.fn(value => value)
}))

// Mock the syncFormElementSampleValue function
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

// Mock the getAttrsFromViewModel and getAttrsFromFormField functions
jest.mock('@model/form/common/utils', () => ({
  getAttrsFromViewModel: jest.fn((model, defaultModel, specialKeys) => {
    // Simple mock implementation that returns a FormField
    return {
      id: model.id,
      type: model.type,
      value: model.value,
      defaultValue: model.defaultValue,
      label: model.label,
      fieldSpecific: {
        rowHeight: model.rowHeight,
        maxLength: model.maxLength,
        minLength: model.minLength,
        hideLabel: model.hideLabel,
        placeholder: model.placeholder,
        required: model.required,
        readonly: model.readonly,
        isProtected: model.isProtected,
        supporting: model.supporting
      }
    }
  }),
  getAttrsFromFormField: jest.fn((data, defaultModel, specialKeys, options) => {
    // Simple mock implementation that returns a FormElementMultiLineTextModel
    const model = {
      ...cloneDeep(defaultModel),
      id: data.id,
      type: data.type,
      value: data.value,
      defaultValue: data.defaultValue,
      label: data.label,
      rowHeight: data.fieldSpecific.rowHeight,
      maxLength: data.fieldSpecific.maxLength,
      minLength: data.fieldSpecific.minLength,
      hideLabel: data.fieldSpecific.hideLabel,
      placeholder: data.fieldSpecific.placeholder,
      required: data.fieldSpecific.required,
      readonly: data.fieldSpecific.readonly,
      isProtected: data.fieldSpecific.isProtected,
      supporting: data.fieldSpecific.supporting
    }

    // Apply transforms if provided
    if (options?.transforms?.value && data.defaultValue) {
      model.value = data.defaultValue
    }

    return model
  }),
  ModelAttrTransforms: jest.fn(),
  useDefaultValue: jest.fn((data) => data.defaultValue)
}))

describe('MultiLineTextFactory', () => {
  let multiLineTextFactory: MultiLineTextFactory
  let mockMultiLineTextModel: FormElementMultiLineTextModel
  let mockFormField: FormField

  beforeEach(() => {
    multiLineTextFactory = new MultiLineTextFactory()

    // Create a mock multi-line text model for testing
    mockMultiLineTextModel = {
      ...MultiLineTextDefaultProps,
      id: 'test-id',
      label: 'Test Multi-line Text',
      value: 'Test value',
      defaultValue: 'Default value',
      rowHeight: '4',
      maxLength: '100',
      minLength: '10',
      required: true,
      placeholder: 'Enter text here'
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'MultiLineText',
      label: 'Test Multi-line Text',
      value: 'Test value',
      defaultValue: 'Default value',
      fieldSpecific: {
        rowHeight: '4',
        maxLength: '100',
        minLength: '10',
        hideLabel: false,
        placeholder: 'Enter text here',
        required: true,
        readonly: false,
        isProtected: false,
        supporting: 'Supporting text'
      }
    }
  })

  describe('create', () => {
    it('should create a new multi-line text model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = multiLineTextFactory.create(option)

      expect(result).toEqual({
        ...MultiLineTextDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(MultiLineTextDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = multiLineTextFactory.create(option)

      expect(result.label).toEqual('translated-label')
      expect(result.placeholder).toEqual('translated-placeholder')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = multiLineTextFactory.toServerData(mockMultiLineTextModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockMultiLineTextModel.id)
      expect(result.type).toEqual(mockMultiLineTextModel.type)
      expect(result.value).toEqual(mockMultiLineTextModel.value)
      expect(result.defaultValue).toEqual(mockMultiLineTextModel.defaultValue)
      expect(result.fieldSpecific.rowHeight).toEqual(mockMultiLineTextModel.rowHeight)
      expect(result.fieldSpecific.maxLength).toEqual(mockMultiLineTextModel.maxLength)
      expect(result.fieldSpecific.minLength).toEqual(mockMultiLineTextModel.minLength)
      expect(result.fieldSpecific.required).toEqual(mockMultiLineTextModel.required)
      expect(result.fieldSpecific.placeholder).toEqual(mockMultiLineTextModel.placeholder)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {}
      const result = multiLineTextFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.type).toEqual(mockFormField.type)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.defaultValue).toEqual(mockFormField.defaultValue)
      expect(result.rowHeight).toEqual(mockFormField.fieldSpecific.rowHeight)
      expect(result.maxLength).toEqual(mockFormField.fieldSpecific.maxLength)
      expect(result.minLength).toEqual(mockFormField.fieldSpecific.minLength)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
      expect(result.placeholder).toEqual(mockFormField.fieldSpecific.placeholder)
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = multiLineTextFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual(mockFormField.defaultValue)
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = multiLineTextFactory.validate(mockMultiLineTextModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockMultiLineTextModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockMultiLineTextModel.value = ''

      const validationPromise = multiLineTextFactory.validate(mockMultiLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockMultiLineTextModel)
      expect(mockMultiLineTextModel.errors).toHaveLength(1)
      expect(mockMultiLineTextModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should not validate when text exceeds maxLength', async () => {
      mockMultiLineTextModel.value = 'a'.repeat(101)
      mockMultiLineTextModel.maxLength = '100'

      const validationPromise = multiLineTextFactory.validate(mockMultiLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockMultiLineTextModel)
      expect(mockMultiLineTextModel.errors).toHaveLength(1)
      expect(mockMultiLineTextModel.errors[0].errorType).toEqual(FormErrorType.MaxLimit)
    })

    it('should not validate when text is shorter than minLength', async () => {
      mockMultiLineTextModel.value = 'short'
      mockMultiLineTextModel.minLength = '10'

      const validationPromise = multiLineTextFactory.validate(mockMultiLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockMultiLineTextModel)
      expect(mockMultiLineTextModel.errors).toHaveLength(1)
      expect(mockMultiLineTextModel.errors[0].errorType).toEqual(FormErrorType.MinLimit)
    })

    it('should validate template even if required field is empty', async () => {
      mockMultiLineTextModel.value = ''
      const option: FormValidateOptions = {
        isValidateTemplate: true
      }

      const validationPromise = multiLineTextFactory.validate(mockMultiLineTextModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockMultiLineTextModel.errors).toEqual([])
    })

    it('should use removeDDRSource when enableDDR is true', async () => {
      const option: FormValidateOptions = {
        enableDDR: true
      }

      await multiLineTextFactory.validate(mockMultiLineTextModel, option)

      expect(require('../shared').removeDDRSource).toHaveBeenCalledWith(mockMultiLineTextModel.value)
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string', () => {
      mockMultiLineTextModel.value = 'Test value'

      multiLineTextFactory.resetValue(mockMultiLineTextModel)

      expect(mockMultiLineTextModel.value).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for normal form', () => {
      const result = multiLineTextFactory.uiOption({})

      expect(result).toEqual({
        view: 'FormMultiLineTextView',
        editor: 'FormMultiLineText'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const result = multiLineTextFactory.uiOption({ isPDFForm: true })

      expect(result).toEqual({
        view: 'PDFFormMultiLineTextView',
        editor: 'FormInputTextSetting'
      })
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      multiLineTextFactory.setValue(mockMultiLineTextModel, 'New value', option)

      expect(mockMultiLineTextModel.value).toEqual('New value')
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id'
      }
      
      multiLineTextFactory.syncValue(mockMultiLineTextModel, 'New value', option)

      expect(mockMultiLineTextModel.value).toEqual('New value')
    })

    it('should not sync the value when focused', () => {
      const option = {
        focusedElementId: 'test-id'
      }
      
      multiLineTextFactory.syncValue(mockMultiLineTextModel, 'New value', option)

      expect(mockMultiLineTextModel.value).toEqual('Test value')
    })
  })
})
